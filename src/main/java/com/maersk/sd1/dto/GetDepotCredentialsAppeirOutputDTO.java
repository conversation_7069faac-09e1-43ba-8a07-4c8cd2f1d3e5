package com.maersk.sd1.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GetDepotCredentialsAppeirOutputDTO {

    private Integer depotCredentialAppeirId;
    private Integer subBusinessUnitId;
    private String url;
    private String clientId;
    private String clientSecret;
    private String shopEmailCopy;
    private Boolean active;
    private Integer userRegistrationId;
    private LocalDateTime registrationDate;
    private Integer userModificationId;
    private LocalDateTime modificationDate;
    private Integer shippingLineId;
    private Integer businessUnitId;
    private Boolean sendGateIn;
    private Boolean sendGateOut;
}

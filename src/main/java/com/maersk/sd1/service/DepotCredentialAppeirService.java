package com.maersk.sd1.service;

import com.maersk.sd1.dto.GetDepotCredentialsAppeirInputDTO;
import com.maersk.sd1.dto.GetDepotCredentialsAppeirOutputDTO;
import com.maersk.sd1.repository.DepotCredentialAppeirRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.persistence.EntityManager;
import jakarta.persistence.ParameterMode;
import jakarta.persistence.StoredProcedureQuery;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class DepotCredentialAppeirService {

    private final DepotCredentialAppeirRepository depotCredentialAppeirRepository;
    private final EntityManager entityManager;

    /**
     * Calls the stored procedure [sde].[get_depot_credentials_appeir] to retrieve all configured depot credentials
     * 
     * @param inputDTO Input parameters for the stored procedure
     * @return List of depot credentials
     */
    public List<GetDepotCredentialsAppeirOutputDTO> getDepotCredentialsAppeir(GetDepotCredentialsAppeirInputDTO inputDTO) {
        log.info("Calling stored procedure [sde].[get_depot_credentials_appeir] with systemAlias: {}", inputDTO.getSystemAlias());
        
        try {
            StoredProcedureQuery query = entityManager.createStoredProcedureQuery("[sde].[get_depot_credentials_appeir]");
            
            // Register parameters
            query.registerStoredProcedureParameter("system_alias", String.class, ParameterMode.IN);
            
            // Set parameter values
            query.setParameter("system_alias", inputDTO.getSystemAlias());
            
            // Execute the stored procedure
            query.execute();
            
            // Get results
            @SuppressWarnings("unchecked")
            List<Object[]> results = query.getResultList();
            
            return mapResultsToDTO(results);
            
        } catch (Exception e) {
            log.error("Error executing stored procedure [sde].[get_depot_credentials_appeir]: {}", e.getMessage(), e);
            throw new RuntimeException("Error retrieving depot credentials", e);
        }
    }

    /**
     * Alternative method using repository native query
     * 
     * @param inputDTO Input parameters for the stored procedure
     * @return List of depot credentials
     */
    public List<GetDepotCredentialsAppeirOutputDTO> getDepotCredentialsAppeirAlternative(GetDepotCredentialsAppeirInputDTO inputDTO) {
        log.info("Calling stored procedure via repository with systemAlias: {}", inputDTO.getSystemAlias());
        
        try {
            List<Object[]> results = depotCredentialAppeirRepository.getDepotCredentialsAppeir(inputDTO.getSystemAlias());
            return mapResultsToDTO(results);
            
        } catch (Exception e) {
            log.error("Error executing stored procedure via repository: {}", e.getMessage(), e);
            throw new RuntimeException("Error retrieving depot credentials", e);
        }
    }

    /**
     * Maps the raw Object[] results from the stored procedure to DTO objects
     * 
     * @param results Raw results from stored procedure
     * @return List of mapped DTOs
     */
    private List<GetDepotCredentialsAppeirOutputDTO> mapResultsToDTO(List<Object[]> results) {
        List<GetDepotCredentialsAppeirOutputDTO> dtoList = new ArrayList<>();
        
        for (Object[] row : results) {
            GetDepotCredentialsAppeirOutputDTO dto = GetDepotCredentialsAppeirOutputDTO.builder()
                    .depotCredentialAppeirId(row[0] != null ? (Integer) row[0] : null)
                    .subBusinessUnitId(row[1] != null ? (Integer) row[1] : null)
                    .url(row[2] != null ? (String) row[2] : null)
                    .clientId(row[3] != null ? (String) row[3] : null)
                    .clientSecret(row[4] != null ? (String) row[4] : null)
                    .shopEmailCopy(row[5] != null ? (String) row[5] : null)
                    .active(row[6] != null ? (Boolean) row[6] : null)
                    .userRegistrationId(row[7] != null ? (Integer) row[7] : null)
                    .registrationDate(row[8] != null ? (LocalDateTime) row[8] : null)
                    .userModificationId(row[9] != null ? (Integer) row[9] : null)
                    .modificationDate(row[10] != null ? (LocalDateTime) row[10] : null)
                    .shippingLineId(row[11] != null ? (Integer) row[11] : null)
                    .businessUnitId(row[12] != null ? (Integer) row[12] : null)
                    .sendGateIn(row[13] != null ? (Boolean) row[13] : null)
                    .sendGateOut(row[14] != null ? (Boolean) row[14] : null)
                    .build();
            
            dtoList.add(dto);
        }
        
        log.info("Successfully mapped {} depot credentials", dtoList.size());
        return dtoList;
    }
}

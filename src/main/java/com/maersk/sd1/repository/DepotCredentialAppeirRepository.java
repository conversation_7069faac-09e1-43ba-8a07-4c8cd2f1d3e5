package com.maersk.sd1.repository;

import com.maersk.sd1.common.model.DepotCredentialAppeir;
import com.maersk.sd1.dto.GetDepotCredentialsAppeirOutputDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DepotCredentialAppeirRepository extends JpaRepository<DepotCredentialAppeir, Integer> {

    @Query(value = "EXEC [sde].[get_depot_credentials_appeir] :systemAlias", nativeQuery = true)
    List<Object[]> getDepotCredentialsAppeir(@Param("systemAlias") String systemAlias);
}

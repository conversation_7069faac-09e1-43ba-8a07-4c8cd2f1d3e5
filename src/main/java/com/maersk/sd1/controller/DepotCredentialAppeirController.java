package com.maersk.sd1.controller;

import com.maersk.sd1.dto.GetDepotCredentialsAppeirInputDTO;
import com.maersk.sd1.dto.GetDepotCredentialsAppeirOutputDTO;
import com.maersk.sd1.service.DepotCredentialAppeirService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/depot-credentials-appeir")
@RequiredArgsConstructor
public class DepotCredentialAppeirController {

    private final DepotCredentialAppeirService depotCredentialAppeirService;

    /**
     * Endpoint to get depot credentials using EntityManager approach
     * 
     * @param inputDTO Input parameters
     * @return List of depot credentials
     */
    @PostMapping("/get")
    public ResponseEntity<List<GetDepotCredentialsAppeirOutputDTO>> getDepotCredentials(
            @Valid @RequestBody GetDepotCredentialsAppeirInputDTO inputDTO) {
        
        List<GetDepotCredentialsAppeirOutputDTO> result = depotCredentialAppeirService.getDepotCredentialsAppeir(inputDTO);
        return ResponseEntity.ok(result);
    }

    /**
     * Endpoint to get depot credentials using Repository approach
     * 
     * @param inputDTO Input parameters
     * @return List of depot credentials
     */
    @PostMapping("/get-alternative")
    public ResponseEntity<List<GetDepotCredentialsAppeirOutputDTO>> getDepotCredentialsAlternative(
            @Valid @RequestBody GetDepotCredentialsAppeirInputDTO inputDTO) {
        
        List<GetDepotCredentialsAppeirOutputDTO> result = depotCredentialAppeirService.getDepotCredentialsAppeirAlternative(inputDTO);
        return ResponseEntity.ok(result);
    }

    /**
     * GET endpoint for simple testing with query parameter
     * 
     * @param systemAlias Optional system alias parameter
     * @return List of depot credentials
     */
    @GetMapping
    public ResponseEntity<List<GetDepotCredentialsAppeirOutputDTO>> getDepotCredentialsSimple(
            @RequestParam(required = false) String systemAlias) {
        
        GetDepotCredentialsAppeirInputDTO inputDTO = GetDepotCredentialsAppeirInputDTO.builder()
                .systemAlias(systemAlias)
                .build();
        
        List<GetDepotCredentialsAppeirOutputDTO> result = depotCredentialAppeirService.getDepotCredentialsAppeir(inputDTO);
        return ResponseEntity.ok(result);
    }
}
